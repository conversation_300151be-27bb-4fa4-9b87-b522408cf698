---
import Layout from '../layouts/Layout.astro'
import FlightSearch from '../components/FlightSearch.astro'
import FlightResults from '../components/FlightResults.astro'

import '@/styles/global.css'
---

<Layout title="深圳航空 - 机票预订">
  <div class="bg-red-400">1111</div>
  <!-- Hero Section with Flight Search -->
  <section class="hero">
    <div class="hero-background">
      <img src="/airplane-interior.jpg" alt="飞机内部" class="hero-image" />
      <div class="hero-overlay"></div>
    </div>
    <FlightSearch />
  </section>

  <!-- Flight Results Section -->
  <section class="results-section">
    <FlightResults />
  </section>
</Layout>

<style>
  .hero {
    position: relative;
    min-height: 400px;
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
  }

  .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(196, 30, 58, 0.8),
      rgba(0, 0, 0, 0.4)
    );
  }

  .results-section {
    background-color: #f5f5f5;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .hero {
      min-height: 300px;
    }
  }
</style>
