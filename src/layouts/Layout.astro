---
export interface Props {
	title?: string;
}

const { title = "深圳航空 - 机票预订" } = Astro.props;
---

<!doctype html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
	</head>
	<body>
		<!-- Cookie Notice -->
		<div class="cookie-notice">
			<div class="cookie-content">
				<span>This website uses Cookies for analytics, personalisation and advertising. Click [Cookie Statement] to learn more or change your Cookie settings. By continuing to browse this website, you agree to our use of Cookies.</span>
				<button class="cookie-close">×</button>
			</div>
		</div>

		<!-- Header -->
		<header class="header">
			<div class="header-container">
				<div class="logo">
					<img src="/logo.png" alt="深圳航空" class="logo-img" />
					<span class="logo-text">深圳航空</span>
				</div>
				<nav class="nav">
					<a href="#" class="nav-item active">Booking</a>
					<a href="#" class="nav-item">Products</a>
					<a href="#" class="nav-item">Self-service</a>
					<a href="#" class="nav-item">Member Services</a>
					<a href="#" class="nav-item">Flight Assistance</a>
					<a href="#" class="nav-item">Contact Us</a>
				</nav>
				<div class="header-actions">
					<span class="help-text">Need help here</span>
					<button class="login-btn">Log in</button>
				</div>
			</div>
		</header>

		<main>
			<slot />
		</main>

		<!-- Footer -->
		<footer class="footer">
			<div class="footer-container">
				<div class="footer-links">
					<a href="#">Legal Notice</a>
					<a href="#">Privacy Notice</a>
					<a href="#">Cookie Statement</a>
					<a href="#">Disclaimer</a>
					<a href="#">Terms of Use</a>
					<a href="#">General conditions of carriage</a>
					<a href="#">Contact Us</a>
				</div>
				<div class="footer-bottom">
					<span>Copyright © Shenzhen Airlines Company Ltd. All Rights Reserved.</span>
					<div class="alliance-logos">
						<span>A STAR ALLIANCE MEMBER</span>
						<img src="/star-alliance-logo.png" alt="Star Alliance" />
					</div>
				</div>
			</div>
		</footer>
	</body>
</html>

<style>
	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	html,
	body {
		font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
		line-height: 1.6;
		color: #333;
		background-color: #f5f5f5;
	}

	/* Cookie Notice */
	.cookie-notice {
		background-color: #8B4513;
		color: white;
		padding: 8px 0;
		font-size: 12px;
		position: relative;
	}

	.cookie-content {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 20px;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.cookie-close {
		background: none;
		border: none;
		color: white;
		font-size: 18px;
		cursor: pointer;
		padding: 0 10px;
	}

	/* Header */
	.header {
		background-color: #c41e3a;
		color: white;
		padding: 0;
	}

	.header-container {
		max-width: 1200px;
		margin: 0 auto;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20px;
		height: 70px;
	}

	.logo {
		display: flex;
		align-items: center;
		gap: 10px;
	}

	.logo-img {
		width: 40px;
		height: 40px;
		background-color: white;
		border-radius: 4px;
		padding: 5px;
	}

	.logo-text {
		font-size: 18px;
		font-weight: 600;
		color: white;
	}

	.nav {
		display: flex;
		gap: 30px;
	}

	.nav-item {
		color: white;
		text-decoration: none;
		font-weight: 500;
		padding: 20px 0;
		border-bottom: 3px solid transparent;
		transition: border-color 0.3s;
	}

	.nav-item:hover,
	.nav-item.active {
		border-bottom-color: white;
	}

	.header-actions {
		display: flex;
		align-items: center;
		gap: 15px;
	}

	.help-text {
		font-size: 14px;
		color: rgba(255, 255, 255, 0.8);
	}

	.login-btn {
		background: rgba(255, 255, 255, 0.2);
		border: 1px solid rgba(255, 255, 255, 0.3);
		color: white;
		padding: 8px 16px;
		border-radius: 4px;
		cursor: pointer;
		font-weight: 500;
		transition: background-color 0.3s;
	}

	.login-btn:hover {
		background: rgba(255, 255, 255, 0.3);
	}

	/* Main Content */
	main {
		min-height: calc(100vh - 200px);
	}

	/* Footer */
	.footer {
		background-color: #2c2c2c;
		color: white;
		padding: 30px 0 20px;
		margin-top: 50px;
	}

	.footer-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 20px;
	}

	.footer-links {
		display: flex;
		gap: 30px;
		margin-bottom: 20px;
		flex-wrap: wrap;
	}

	.footer-links a {
		color: #ccc;
		text-decoration: none;
		font-size: 14px;
		transition: color 0.3s;
	}

	.footer-links a:hover {
		color: white;
	}

	.footer-bottom {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-top: 20px;
		border-top: 1px solid #444;
		font-size: 12px;
		color: #999;
	}

	.alliance-logos {
		display: flex;
		align-items: center;
		gap: 10px;
	}

	.alliance-logos img {
		height: 20px;
	}

	/* Responsive */
	@media (max-width: 768px) {
		.header-container {
			flex-direction: column;
			height: auto;
			padding: 15px 20px;
		}

		.nav {
			gap: 20px;
			margin: 10px 0;
		}

		.nav-item {
			font-size: 14px;
		}

		.footer-links {
			gap: 15px;
		}

		.footer-bottom {
			flex-direction: column;
			gap: 10px;
		}
	}
</style>
