---
// Flight search form component
---

<div class="flight-search">
	<div class="search-container">
		<div class="trip-type">
			<label class="radio-option">
				<input type="radio" name="tripType" value="oneWay" checked />
				<span>One-way</span>
			</label>
			<label class="radio-option">
				<input type="radio" name="tripType" value="roundTrip" />
				<span>Round-trip</span>
			</label>
		</div>

		<div class="search-form">
			<div class="form-row">
				<div class="form-group">
					<label>From</label>
					<div class="location-input">
						<input type="text" value="Beijing" readonly />
						<span class="location-code">PEK</span>
					</div>
				</div>

				<button class="swap-btn" type="button">⇄</button>

				<div class="form-group">
					<label>To</label>
					<div class="location-input">
						<input type="text" value="Guangzhou" readonly />
						<span class="location-code">CAN</span>
					</div>
				</div>

				<div class="form-group">
					<label>Departure</label>
					<input type="date" value="2025-06-14" />
				</div>

				<div class="form-group">
					<label>Return</label>
					<input type="date" disabled />
				</div>

				<div class="form-group">
					<label>Passengers</label>
					<select>
						<option>1 Adult</option>
						<option>2 Adults</option>
						<option>3 Adults</option>
					</select>
				</div>

				<button class="search-btn" type="submit">
					<span class="search-icon">🔍</span>
					Search
				</button>
			</div>
		</div>

		<div class="search-options">
			<label class="checkbox-option">
				<input type="checkbox" />
				<span>Edit search</span>
			</label>
			<label class="checkbox-option">
				<input type="checkbox" />
				<span>Booking</span>
			</label>
			<label class="checkbox-option">
				<input type="checkbox" />
				<span>Information</span>
			</label>
			<label class="checkbox-option">
				<input type="checkbox" />
				<span>Self-service</span>
			</label>
			<label class="checkbox-option">
				<input type="checkbox" />
				<span>My</span>
			</label>
			<label class="checkbox-option">
				<input type="checkbox" />
				<span>Complaint</span>
			</label>
		</div>
	</div>
</div>

<style>
	.flight-search {
		background: linear-gradient(135deg, rgba(0,0,0,0.3), rgba(0,0,0,0.1));
		padding: 30px 0;
		position: relative;
	}

	.search-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 20px;
	}

	.trip-type {
		display: flex;
		gap: 20px;
		margin-bottom: 20px;
	}

	.radio-option {
		display: flex;
		align-items: center;
		gap: 8px;
		color: white;
		font-weight: 500;
		cursor: pointer;
	}

	.radio-option input[type="radio"] {
		width: 16px;
		height: 16px;
	}

	.search-form {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 8px;
		padding: 25px;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	}

	.form-row {
		display: flex;
		gap: 15px;
		align-items: end;
		flex-wrap: wrap;
	}

	.form-group {
		display: flex;
		flex-direction: column;
		min-width: 140px;
		flex: 1;
	}

	.form-group label {
		font-size: 14px;
		font-weight: 500;
		color: #666;
		margin-bottom: 8px;
	}

	.location-input {
		position: relative;
		display: flex;
		align-items: center;
	}

	.location-input input {
		width: 100%;
		padding: 12px 50px 12px 12px;
		border: 2px solid #e0e0e0;
		border-radius: 6px;
		font-size: 16px;
		background: white;
	}

	.location-code {
		position: absolute;
		right: 12px;
		color: #999;
		font-size: 14px;
		font-weight: 500;
	}

	.form-group input,
	.form-group select {
		padding: 12px;
		border: 2px solid #e0e0e0;
		border-radius: 6px;
		font-size: 16px;
		background: white;
		transition: border-color 0.3s;
	}

	.form-group input:focus,
	.form-group select:focus {
		outline: none;
		border-color: #c41e3a;
	}

	.form-group input:disabled {
		background-color: #f5f5f5;
		color: #999;
		cursor: not-allowed;
	}

	.swap-btn {
		background: #c41e3a;
		color: white;
		border: none;
		border-radius: 50%;
		width: 40px;
		height: 40px;
		cursor: pointer;
		font-size: 18px;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: background-color 0.3s;
		margin-bottom: 5px;
	}

	.swap-btn:hover {
		background: #a01729;
	}

	.search-btn {
		background: #c41e3a;
		color: white;
		border: none;
		border-radius: 6px;
		padding: 12px 24px;
		font-size: 16px;
		font-weight: 600;
		cursor: pointer;
		display: flex;
		align-items: center;
		gap: 8px;
		transition: background-color 0.3s;
		min-width: 120px;
		justify-content: center;
	}

	.search-btn:hover {
		background: #a01729;
	}

	.search-icon {
		font-size: 18px;
	}

	.search-options {
		display: flex;
		gap: 25px;
		margin-top: 20px;
		flex-wrap: wrap;
	}

	.checkbox-option {
		display: flex;
		align-items: center;
		gap: 8px;
		color: white;
		font-size: 14px;
		cursor: pointer;
	}

	.checkbox-option input[type="checkbox"] {
		width: 16px;
		height: 16px;
	}

	/* Responsive */
	@media (max-width: 768px) {
		.form-row {
			flex-direction: column;
		}

		.form-group {
			min-width: 100%;
		}

		.swap-btn {
			align-self: center;
			margin: 10px 0;
		}

		.search-options {
			gap: 15px;
		}
	}
</style>

<script>
	// Handle trip type change
	document.addEventListener('DOMContentLoaded', function() {
		const tripTypeRadios = document.querySelectorAll('input[name="tripType"]');
		const returnInput = document.querySelector('input[type="date"]:last-of-type');
		
		tripTypeRadios.forEach(radio => {
			radio.addEventListener('change', function() {
				if (this.value === 'oneWay') {
					returnInput.disabled = true;
					returnInput.value = '';
				} else {
					returnInput.disabled = false;
				}
			});
		});

		// Handle swap button
		const swapBtn = document.querySelector('.swap-btn');
		const fromInput = document.querySelector('.location-input:first-of-type input');
		const toInput = document.querySelector('.location-input:last-of-type input');
		const fromCode = document.querySelector('.location-input:first-of-type .location-code');
		const toCode = document.querySelector('.location-input:last-of-type .location-code');

		swapBtn.addEventListener('click', function() {
			const tempValue = fromInput.value;
			const tempCode = fromCode.textContent;
			
			fromInput.value = toInput.value;
			fromCode.textContent = toCode.textContent;
			toInput.value = tempValue;
			toCode.textContent = tempCode;
		});
	});
</script>
