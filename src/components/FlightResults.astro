---
// Flight results component
const flights = [
	{
		id: 1,
		departure: {
			time: "12:30",
			airport: "Shenzhen Bao'an International Airport",
			code: "SZX"
		},
		arrival: {
			time: "14:35",
			airport: "Beijing Capital International Airport",
			code: "PEK"
		},
		duration: "2h 5min",
		flightNumber: "ZH6034",
		aircraft: "Airbus A320",
		stops: 0,
		prices: {
			premium: { price: 13999, available: true },
			economy: { price: 13999, available: true },
			business: { price: 13999, available: true }
		}
	},
	{
		id: 2,
		departure: {
			time: "12:30",
			airport: "Shenzhen Bao'an International Airport",
			code: "SZX"
		},
		arrival: {
			time: "14:35",
			airport: "Beijing Capital International Airport",
			code: "PEK"
		},
		duration: "2h 5min",
		stay: "1h 35min",
		flightNumber: "ZH6034",
		aircraft: "Airbus A320",
		stops: 1,
		transfer: "Transfer for 1 Lanna",
		remaining: "2 Remaining",
		prices: {
			premium: { price: 13999, available: true },
			economy: { price: 13999, available: true },
			business: { price: 13999, available: true }
		}
	}
];
---

<div class="flight-results">
	<div class="results-container">
		<!-- Date selector -->
		<div class="date-selector">
			<div class="date-tabs">
				<button class="date-tab">CNY 1,920<br><span>06-13 Sun</span></button>
				<button class="date-tab">CNY 1,920<br><span>06-14 Mon</span></button>
				<button class="date-tab">CNY 1,920<br><span>06-15 Tue</span></button>
				<button class="date-tab active">CNY 1,920<br><span>06-16 Wed</span></button>
				<button class="date-tab">CNY 1,920<br><span>06-17 Thu</span></button>
				<button class="date-tab">CNY 1,920<br><span>06-18 Fri</span></button>
				<button class="date-tab">CNY 1,920<br><span>06-21 Sat</span></button>
			</div>
			<div class="sort-options">
				<span>Time ↕</span>
				<span>price ↕</span>
			</div>
			<div class="price-calendar">
				📅 Price calendar
			</div>
		</div>

		<!-- Flight list -->
		<div class="flight-list">
			{flights.map(flight => (
				<div class="flight-card">
					<div class="flight-info">
						<div class="flight-time">
							<div class="departure">
								<div class="time">{flight.departure.time}</div>
								<div class="airport">{flight.departure.airport}</div>
							</div>
							
							<div class="flight-path">
								<div class="duration">{flight.duration}</div>
								{flight.stay && <div class="stay">Stay: {flight.stay}</div>}
								<div class="path-line">
									<div class="line"></div>
									{flight.stops > 0 && <div class="stop-dot"></div>}
									<div class="plane-icon">✈</div>
								</div>
								{flight.stops > 0 && <div class="transfer">{flight.transfer}</div>}
							</div>
							
							<div class="arrival">
								<div class="time">{flight.arrival.time}</div>
								<div class="airport">{flight.arrival.airport}</div>
							</div>
						</div>

						<div class="flight-details">
							<div class="flight-number">
								<span class="airline-logo">🛩</span>
								{flight.flightNumber}
							</div>
							<div class="aircraft">{flight.aircraft}</div>
							{flight.remaining && <div class="remaining">{flight.remaining}</div>}
						</div>
					</div>

					<div class="price-options">
						<div class="class-tabs">
							<button class="class-tab premium">🏆 Premium Economy</button>
							<button class="class-tab economy active">💺 Economy Class</button>
							<button class="class-tab business">💼 Business Class</button>
						</div>

						<div class="price-cards">
							<div class="price-card economy-card">
								<div class="price-header">CNY</div>
								<div class="price-amount">13,999</div>
								<div class="price-label">upwards</div>
								<div class="price-details">
									<div class="detail-row">
										<span>Taxes and Fees</span>
										<span>CNY 7,099</span>
									</div>
									<div class="detail-row">
										<span>Fare 💰</span>
										<span>CNY 900</span>
									</div>
									<div class="detail-row">
										<span>M Cabin Class</span>
										<span>U JA</span>
									</div>
									<div class="detail-row">
										<span>M Meal</span>
										<span>HMF</span>
									</div>
									<div class="detail-row">
										<span>📦 Baggage</span>
										<span>AN ITEM</span>
									</div>
									<div class="detail-row">
										<span>🔄 Refund</span>
										<span class="link">Details</span>
									</div>
									<div class="detail-row">
										<span>🔄 Change</span>
										<span class="link">Details</span>
									</div>
									<div class="detail-row">
										<span>📦 Base Mileage</span>
										<span>+1142</span>
									</div>
									<div class="detail-row">
										<span>⭐ Bonus Mileage</span>
										<span>+1142</span>
									</div>
								</div>
								<button class="book-btn">📋 BOOK</button>
							</div>

							<div class="price-card business-card">
								<div class="class-label">Business Travel Economy Class</div>
								<div class="price-header">CNY</div>
								<div class="price-amount">7,999</div>
								<div class="price-label">upwards</div>
								<div class="price-details">
									<div class="detail-row">
										<span>Taxes and Fees</span>
										<span>CNY 7,099</span>
									</div>
									<div class="detail-row">
										<span>Fare 💰</span>
										<span>CNY 900</span>
									</div>
									<div class="detail-row">
										<span>M Cabin Class</span>
										<span>U JA</span>
									</div>
									<div class="detail-row">
										<span>M Meal</span>
										<span>HMF</span>
									</div>
									<div class="detail-row">
										<span>📦 Baggage</span>
										<span>AN ITEM</span>
									</div>
									<div class="detail-row">
										<span>🔄 Refund</span>
										<span class="link">Details</span>
									</div>
									<div class="detail-row">
										<span>🔄 Change</span>
										<span class="link">Details</span>
									</div>
									<div class="detail-row">
										<span>📦 Base Mileage</span>
										<span>+1142</span>
									</div>
									<div class="detail-row">
										<span>⭐ Bonus Mileage</span>
										<span>+1142</span>
									</div>
								</div>
								<button class="book-btn">📋 BOOK</button>
							</div>

							<div class="price-card super-value">
								<div class="class-label">Eco Super Value</div>
								<div class="price-header">CNY</div>
								<div class="price-amount">7,999</div>
								<div class="price-label">upwards</div>
								<button class="book-btn">📋 BOOK</button>
							</div>
						</div>
					</div>
				</div>
			))}
		</div>
	</div>
</div>

<style>
	.flight-results {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding: 20px 0;
	}

	.results-container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 20px;
	}

	/* Date Selector */
	.date-selector {
		background: white;
		border-radius: 8px;
		padding: 20px;
		margin-bottom: 20px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		gap: 20px;
	}

	.date-tabs {
		display: flex;
		gap: 10px;
	}

	.date-tab {
		background: #f8f9fa;
		border: 1px solid #e0e0e0;
		border-radius: 6px;
		padding: 10px 15px;
		cursor: pointer;
		font-size: 14px;
		text-align: center;
		transition: all 0.3s;
		min-width: 80px;
	}

	.date-tab.active {
		background: #c41e3a;
		color: white;
		border-color: #c41e3a;
	}

	.date-tab span {
		font-size: 12px;
		color: #666;
	}

	.date-tab.active span {
		color: rgba(255, 255, 255, 0.8);
	}

	.sort-options {
		display: flex;
		gap: 20px;
		font-size: 14px;
		color: #666;
	}

	.sort-options span {
		cursor: pointer;
		padding: 5px 10px;
		border-radius: 4px;
		transition: background-color 0.3s;
	}

	.sort-options span:hover {
		background-color: #f0f0f0;
	}

	.price-calendar {
		color: #c41e3a;
		cursor: pointer;
		font-weight: 500;
	}

	/* Flight Cards */
	.flight-list {
		display: flex;
		flex-direction: column;
		gap: 20px;
	}

	.flight-card {
		background: white;
		border-radius: 8px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}

	.flight-info {
		padding: 25px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1px solid #f0f0f0;
	}

	.flight-time {
		display: flex;
		align-items: center;
		gap: 40px;
		flex: 1;
	}

	.departure, .arrival {
		text-align: center;
	}

	.time {
		font-size: 24px;
		font-weight: 600;
		color: #333;
		margin-bottom: 5px;
	}

	.airport {
		font-size: 14px;
		color: #666;
		max-width: 200px;
	}

	.flight-path {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 5px;
		min-width: 150px;
	}

	.duration {
		font-size: 14px;
		color: #666;
		font-weight: 500;
	}

	.stay {
		font-size: 12px;
		color: #999;
	}

	.path-line {
		display: flex;
		align-items: center;
		position: relative;
		width: 120px;
		margin: 10px 0;
	}

	.line {
		flex: 1;
		height: 2px;
		background: #ddd;
		position: relative;
	}

	.stop-dot {
		width: 8px;
		height: 8px;
		background: #c41e3a;
		border-radius: 50%;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}

	.plane-icon {
		position: absolute;
		right: -10px;
		color: #c41e3a;
		font-size: 16px;
	}

	.transfer {
		font-size: 12px;
		color: #c41e3a;
		text-align: center;
	}

	.flight-details {
		text-align: center;
	}

	.flight-number {
		display: flex;
		align-items: center;
		gap: 5px;
		font-weight: 600;
		margin-bottom: 5px;
	}

	.airline-logo {
		color: #c41e3a;
	}

	.aircraft {
		font-size: 14px;
		color: #666;
		margin-bottom: 5px;
	}

	.remaining {
		font-size: 12px;
		color: #c41e3a;
		font-weight: 500;
	}

	/* Price Options */
	.price-options {
		padding: 0;
	}

	.class-tabs {
		display: flex;
		background: #f8f9fa;
		border-bottom: 1px solid #e0e0e0;
	}

	.class-tab {
		flex: 1;
		padding: 15px;
		background: none;
		border: none;
		cursor: pointer;
		font-size: 14px;
		font-weight: 500;
		transition: all 0.3s;
		border-bottom: 3px solid transparent;
	}

	.class-tab.active {
		background: white;
		border-bottom-color: #c41e3a;
		color: #c41e3a;
	}

	.class-tab.premium {
		color: #ff6b35;
	}

	.class-tab.business {
		color: #2c5aa0;
	}

	.price-cards {
		display: flex;
		padding: 20px;
		gap: 20px;
	}

	.price-card {
		flex: 1;
		background: #f8f9fa;
		border-radius: 8px;
		padding: 20px;
		text-align: center;
		position: relative;
	}

	.economy-card {
		background: linear-gradient(135deg, #fff5f5, #ffeaea);
		border: 2px solid #c41e3a;
	}

	.business-card {
		background: linear-gradient(135deg, #f0f4ff, #e6f0ff);
	}

	.super-value {
		background: linear-gradient(135deg, #fff8e1, #ffecb3);
	}

	.class-label {
		font-size: 12px;
		color: #666;
		margin-bottom: 10px;
		font-weight: 500;
	}

	.price-header {
		font-size: 14px;
		color: #666;
		margin-bottom: 5px;
	}

	.price-amount {
		font-size: 28px;
		font-weight: 700;
		color: #c41e3a;
		margin-bottom: 5px;
	}

	.price-label {
		font-size: 12px;
		color: #666;
		margin-bottom: 15px;
	}

	.price-details {
		text-align: left;
		margin-bottom: 20px;
	}

	.detail-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 4px 0;
		font-size: 12px;
		border-bottom: 1px solid rgba(0, 0, 0, 0.05);
	}

	.detail-row:last-child {
		border-bottom: none;
	}

	.link {
		color: #c41e3a;
		cursor: pointer;
		text-decoration: underline;
	}

	.book-btn {
		background: #c41e3a;
		color: white;
		border: none;
		border-radius: 6px;
		padding: 12px 24px;
		font-weight: 600;
		cursor: pointer;
		transition: background-color 0.3s;
		width: 100%;
		font-size: 14px;
	}

	.book-btn:hover {
		background: #a01729;
	}

	/* Responsive */
	@media (max-width: 768px) {
		.date-selector {
			flex-direction: column;
			align-items: stretch;
		}

		.date-tabs {
			overflow-x: auto;
			padding-bottom: 10px;
		}

		.flight-info {
			flex-direction: column;
			gap: 20px;
		}

		.flight-time {
			gap: 20px;
		}

		.price-cards {
			flex-direction: column;
		}

		.class-tabs {
			flex-direction: column;
		}
	}
</style>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		// Handle class tab switching
		const classTabs = document.querySelectorAll('.class-tab');
		classTabs.forEach(tab => {
			tab.addEventListener('click', function() {
				// Remove active class from all tabs
				classTabs.forEach(t => t.classList.remove('active'));
				// Add active class to clicked tab
				this.classList.add('active');
			});
		});

		// Handle date tab switching
		const dateTabs = document.querySelectorAll('.date-tab');
		dateTabs.forEach(tab => {
			tab.addEventListener('click', function() {
				// Remove active class from all tabs
				dateTabs.forEach(t => t.classList.remove('active'));
				// Add active class to clicked tab
				this.classList.add('active');
			});
		});

		// Handle book button clicks
		const bookButtons = document.querySelectorAll('.book-btn');
		bookButtons.forEach(button => {
			button.addEventListener('click', function() {
				alert('预订功能即将开放，敬请期待！');
			});
		});

		// Handle detail links
		const detailLinks = document.querySelectorAll('.link');
		detailLinks.forEach(link => {
			link.addEventListener('click', function() {
				alert('详细信息：' + this.textContent);
			});
		});
	});
</script>
